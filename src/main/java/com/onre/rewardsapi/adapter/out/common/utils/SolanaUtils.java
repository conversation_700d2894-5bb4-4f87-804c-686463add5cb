package com.onre.rewardsapi.adapter.out.solscan.utils;

import com.onre.rewardsapi.application.common.wrapper.SolAddressWrapper;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class SolanaUtils {
    public static final String SOLANA_DEFAULT_ADDRESS = "11111111111111111111111111111111";

    public static SolAddressWrapper wrapOrDefault(String address) {
        return address == null || address.isBlank()
                ? new SolAddressWrapper(SolanaUtils.SOLANA_DEFAULT_ADDRESS)
                : new SolAddressWrapper(address);
    }
}
