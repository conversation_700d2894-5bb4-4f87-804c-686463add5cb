package com.onre.rewardsapi.adapter.in.quartz;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * Enum representing different types of jobs in the system.
 * Each job type has a name, group, and description.
 */
@Getter
@RequiredArgsConstructor
public enum JobType {
    SOLANA_BLOCK_SCAN("solana-block-scan", "solana", "Scan for new Solana blocks");

    /**
     * The name of the job
     */
    private final String jobName;

    /**
     * The group the job belongs to
     */
    private final String jobGroup;

    /**
     * Description of what the job does
     */
    private final String description;
}
