package com.onre.rewardsapi.application.module.tokentransfer.exception;

import com.onre.rewardsapi.infrastructure.exception.ApiException;
import com.onre.rewardsapi.infrastructure.exception.ErrorType;

public class BlockNotFoundException extends ApiException {

    public BlockNotFoundException(String message) {
        super(message);
    }

    @Override
    protected ErrorType getErrorType() {
        return SolanaTokenTransferPageErrorType.BLOCK_NOT_FOUND;
    }

    @Override
    protected Module getModule() {
        return Module.BLOCK;
    }
}
