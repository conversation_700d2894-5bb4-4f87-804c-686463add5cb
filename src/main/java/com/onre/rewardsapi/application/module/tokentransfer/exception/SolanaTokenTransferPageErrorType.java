package com.onre.rewardsapi.application.module.block.exception;

import com.onre.rewardsapi.infrastructure.exception.ErrorType;
import lombok.Getter;
import lombok.RequiredArgsConstructor;


@Getter
@RequiredArgsConstructor
public enum BlockErrorType implements ErrorType {

    BLOCK_NOT_FOUND("100", "Block not found."),
    ;


    private final String code;

    /**
     * Error custom message
     */
    private final String message;
}
