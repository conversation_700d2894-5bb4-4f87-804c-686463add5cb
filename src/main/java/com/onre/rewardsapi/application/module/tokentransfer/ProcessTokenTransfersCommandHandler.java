package com.onre.rewardsapi.application.module.tokentransfer;

import com.onre.rewardsapi.application.common.command.CommandHandler;
import com.onre.rewardsapi.application.common.properties.TokenProperties;
import com.onre.rewardsapi.application.module.tokentransfer.command.ProcessTokenTransferCommand;
import com.onre.rewardsapi.application.module.tokentransfer.constant.TokenTransferAdvisoryLock;
import com.onre.rewardsapi.application.module.tokentransfer.finder.SolanaTokenTransferPageFinderService;
import com.onre.rewardsapi.application.module.tokentransfer.port.out.GetTokenTransfers;
import com.onre.rewardsapi.domain.solanatokentransferpage.SolanaTokenTransferPage;
import com.onre.rewardsapi.domain.solanatokentransferpage.SolanaTokenTransferPageCreateService;
import com.onre.rewardsapi.domain.tokentransfer.TokenTransferCreateService;
import com.onre.rewardsapi.infrastructure.lock.AdvisoryLockService;
import com.onre.rewardsapi.infrastructure.lock.constant.ModuleLockNamespace;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class ProcessTokenTransfersCommandHandler implements CommandHandler<Void, ProcessTokenTransferCommand> {

    // Do not change this! Tt's the max page size solscan allows
    // If this changes, the history in database gets corrupted
    private static final int DEFAULT_PAGE_SIZE = 100;

    private final GetTokenTransfers getTokenTransfers;
    private final AdvisoryLockService advisoryLockService;
    private final SolanaTokenTransferPageFinderService solanaTokenTransferPageFinderService;
    private final SolanaTokenTransferPageCreateService solanaTokenTransferPageCreateService;
    private final TokenTransferCreateService tokenTransferCreateService;
    private final TokenProperties tokenProperties;


    @Lazy
    @Autowired
    private ProcessTokenTransfersCommandHandler self;

    @Override
    public Class<ProcessTokenTransferCommand> getCommandType() {
        return ProcessTokenTransferCommand.class;
    }

    @Override
    public Void handle(ProcessTokenTransferCommand command) {
        List<SolanaTokenTransferPage> processedPages = solanaTokenTransferPageFinderService.findAll();
        SolanaTokenTransferPage currentPage = processedPages.isEmpty()
                ? solanaTokenTransferPageCreateService.create(1, null, false)
                : processedPages.getFirst();

        int page = Boolean.TRUE.equals(currentPage.getCompletePageProcessed())
                ? currentPage.getLastProcessedPage() + 1
                : currentPage.getLastProcessedPage();

        int lastProcessedPage = page;
        String lastProcessedSignature = currentPage.getLastProcessedTransactionSignature();
        boolean completePageProcessed = false;

        while (true) {
            GetTokenTransfers.Result result = getTokenTransfers.invoke(new GetTokenTransfers.Input(
                    tokenProperties.one().address(),
                    page,
                    DEFAULT_PAGE_SIZE
            ));

            List<GetTokenTransfers.Result.Transaction> transactions = result.transactions();

            if (transactions.isEmpty()) {
                lastProcessedSignature = null;
                lastProcessedPage--; // rollback if no transactions
                break;
            }

            List<GetTokenTransfers.Result.Transaction> txsToSave = lastProcessedSignature == null
                    ? transactions
                    : transactions.stream()
                    .dropWhile(tx -> !tx.signature().equals(currentPage.getLastProcessedTransactionSignature()))
                    .skip(1).toList();

            self.saveTransactions(txsToSave);

            completePageProcessed = result.transactions().size() == DEFAULT_PAGE_SIZE;

            if (!completePageProcessed) {
                lastProcessedSignature = result.transactions().getLast().signature();
                lastProcessedPage = page;
                break;
            }

            lastProcessedSignature = null;
            page++;
        }

        self.updateProcessedPage(lastProcessedPage, lastProcessedSignature, completePageProcessed);
        return null;
    }

    @Transactional
    public void saveTransactions(List<GetTokenTransfers.Result.Transaction> transactions) {
        transactions.forEach(tx -> tokenTransferCreateService.create(
                tx.fromSolAddress(),
                tx.toSolAddress(),
                tx.fromTokenAccount(),
                tx.toTokenAccount(),
                tx.signature(),
                tx.amount(),
                tx.timestamp()
        ));
    }

    @Transactional
    public void updateProcessedPage(Integer page, String lastProcessedTransactionSignature, Boolean completePageProcessed) {
        advisoryLockService.lock(ModuleLockNamespace.TOKEN_TRANSFER, TokenTransferAdvisoryLock.UPDATE_PROCESSED_PAGE.name(), page);
        solanaTokenTransferPageFinderService.findAll()
                .stream()
                .findFirst()
                .ifPresent(processedPage -> processedPage.updatePageInfo(page, lastProcessedTransactionSignature, completePageProcessed));
    }
}
