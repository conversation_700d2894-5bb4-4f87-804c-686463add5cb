package com.onre.rewardsapi.application.module.tokentransfer.port.out;

import com.onre.rewardsapi.application.common.wrapper.SolAddressWrapper;

import java.math.BigInteger;
import java.time.Instant;
import java.util.List;

public interface GetTokenTransfers {
    Result invoke(Input input);

    record Input(
            SolAddressWrapper tokenAddress,
            Integer page
    ) {
    }

    record Result(
            List<Transaction> transactions
    ) {
        public record Transaction(
                String signature,
                SolAddressWrapper fromSolAddress,
                SolAddressWrapper toSolAddress,
                SolAddressWrapper fromTokenAccount,
                SolAddressWrapper toTokenAccount,
                BigInteger amount,
                Instant timestamp
        ) {
        }
    }
}
