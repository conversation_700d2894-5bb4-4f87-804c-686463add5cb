package com.onre.rewardsapi.application.common.properties;


import com.onre.rewardsapi.application.common.wrapper.SolAddressWrapper;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

@Validated
@ConfigurationProperties(prefix = "onre.token")
public record TokenProperties(
        @NotNull Token one
) {
    public record Token(
            @NotNull SolAddressWrapper address,
            @NotNull Long tokenBlockTge
    ) {
    }
}
