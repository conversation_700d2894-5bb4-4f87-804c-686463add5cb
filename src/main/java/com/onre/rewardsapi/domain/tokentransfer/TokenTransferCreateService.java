package com.onre.rewardsapi.domain.tokentransfer;

import com.onre.rewardsapi.application.common.wrapper.SolAddressWrapper;
import com.onre.rewardsapi.domain.tokentransfer.repository.TokenTransferRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.time.Instant;

@Service
@RequiredArgsConstructor
public class TokenTransferCreateService {

    private final TokenTransferRepository tokenTransferRepository;

    @Transactional
    public TokenTransfer create(SolAddressWrapper fromSolAddress,
                                SolAddressWrapper toSolAddress,
                                SolAddressWrapper fromSolTokenAccount,
                                SolAddressWrapper toSolTokenAccount,
                                String signature,
                                BigInteger amount,
                                Instant timestamp) {
        return tokenTransferRepository.save(new TokenTransfer(
                fromSolAddress,
                toSolAddress,
                fromSolTokenAccount,
                toSolTokenAccount,
                signature,
                amount,
                timestamp
        ));
    }
}
