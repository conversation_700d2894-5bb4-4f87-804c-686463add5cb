package com.onre.rewardsapi.domain.tokentransfer;

import com.onre.rewardsapi.application.common.converter.SolAddressWrapperConverter;
import com.onre.rewardsapi.application.common.wrapper.SolAddressWrapper;
import com.onre.rewardsapi.domain.CreatableEntity;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigInteger;
import java.time.Instant;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TokenTransfer extends CreatableEntity {

    @Convert(converter = SolAddressWrapperConverter.class)
    private SolAddressWrapper fromSolAddress;

    @Convert(converter = SolAddressWrapperConverter.class)
    private SolAddressWrapper toSolAddress;

    @Convert(converter = SolAddressWrapperConverter.class)
    private SolAddressWrapper fromSolTokenAddress;

    @Convert(converter = SolAddressWrapperConverter.class)
    private SolAddressWrapper toSolTokenAddress;

    private String transactionSignature;

    private BigInteger amount;

    private Instant timestamp;
}
