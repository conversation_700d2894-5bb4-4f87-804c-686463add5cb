server.port: ${PORT:8080}

spring:
  application:
    name: onre-rewards-api

  liquibase:
    change-log: "classpath:db/changelog/db.changelog.xml"
    default-schema: public

  jpa:
    show-sql: false
    open-in-view: false
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          time_zone: UTC
          batch_size: 20
        order_updates: true
        order_inserts: true
        batch_versioned_data: true

  datasource:
    url: *********************************************?reWriteBatchedInserts=true
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}

  threads:
    virtual:
      enabled: false

  quartz:
    job-store-type: jdbc
    jdbc:
      initialize-schema: never
    overwrite-existing-jobs: true
    wait-for-jobs-to-complete-on-shutdown: true
    auto-startup: true
    properties:
      org:
        quartz:
          scheduler:
            instanceName: ClusteredScheduler
            instanceId: AUTO
            idleWaitTime: 5000
            overwriteExistingjobs: true
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 5
            threadPriority: 5
          jobStore:
            acquireTriggersWithinLock: true
            misfireThreshold: 60000
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            driverDelegateClass: org.quartz.impl.jdbcjobstore.PostgreSQLDelegate
            useProperties: false
            tablePrefix: QRTZ_
            isClustered: true
            clusterCheckinInterval: 60000
            dataSource: dataSource
          dataSource:
            dataSource:
              provider: hikaricp
              autoCommit: false
              class: org.quartz.utils.PoolingConnectionProvider
              driver: org.postgresql.Driver
              URL: *********************************************
              user: ${DB_USERNAME}
              password: ${DB_PASSWORD}
              maxConnections: 5
              validationQuery: SELECT 1

quartz:
  jobs:
    - job-type: SOLSCAN_TOKEN_TRANSFER
      enabled: true
      cron: "0 0 * * * ?"
  enabled: true

springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /api-docs/swagger-ui.html

security:
  cors:
    allow-credentials: true
    allowed-origins:
      - "*"
    allowed-methods: "*"
    allowed-headers: "*"

server:
  max-http-request-header-size: 32KB

management:
  health:
    diskspace.enabled: false
  server:
    port: ${PORT:8080}
  endpoints:
    web:
      exposure:
        include: '*'

logging:
  level:
    com.zaxxer.hikari.HikariConfig: DEBUG

onre:
  token:
    one:
      address: 5Y8NV33Vv7WbnLfq3zBcKSdYPrk7g2KoiQoe7M2tcxp5
      token-block-tge: 331308318

integration:
  solscan:
    api-key: ${SOLSCAN_API_KEY}
    base-url: https://pro-api.solscan.io/v2.0